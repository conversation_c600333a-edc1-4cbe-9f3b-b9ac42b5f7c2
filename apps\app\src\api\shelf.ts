/**
 * 货架管理 API
 */

export interface Shelf {
  id: string;
  name: string;
  location: string;
  capacity: number;
  currentStock: number;
  category: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ShelfQuery {
  page?: number;
  pageSize?: number;
  name?: string;
  location?: string;
  category?: string;
}

export interface ShelfResponse {
  success: boolean;
  data?: Shelf[];
  total?: number;
  message?: string;
}

// VTJ 期望的类型别名
export type ShelfData = Shelf;
export type ShelfListParams = ShelfQuery;

// 添加 VTJ 期望的函数别名
export const addShelf = createShelf;

/**
 * 获取货架列表
 */
export async function getShelfList(query: ShelfQuery = {}): Promise<ShelfResponse> {
  try {
    // 模拟 API 调用
    const mockShelves: Shelf[] = [
      {
        id: '1',
        name: '货架A-001',
        location: '仓库A区',
        capacity: 100,
        currentStock: 75,
        category: '电子产品',
        description: '存放电子产品的货架',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        name: '货架B-002',
        location: '仓库B区',
        capacity: 150,
        currentStock: 120,
        category: '日用品',
        description: '存放日用品的货架',
        createdAt: '2024-01-02T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z'
      }
    ];

    return {
      success: true,
      data: mockShelves,
      total: mockShelves.length
    };
  } catch (error) {
    return {
      success: false,
      message: '获取货架列表失败'
    };
  }
}

/**
 * 获取货架详情
 */
export async function getShelfById(id: string): Promise<ShelfResponse> {
  try {
    // 模拟 API 调用
    const mockShelf: Shelf = {
      id,
      name: '货架A-001',
      location: '仓库A区',
      capacity: 100,
      currentStock: 75,
      category: '电子产品',
      description: '存放电子产品的货架',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    };

    return {
      success: true,
      data: [mockShelf]
    };
  } catch (error) {
    return {
      success: false,
      message: '获取货架详情失败'
    };
  }
}

/**
 * 创建货架
 */
export async function createShelf(shelf: Omit<Shelf, 'id' | 'createdAt' | 'updatedAt'>): Promise<ShelfResponse> {
  try {
    // 模拟 API 调用
    const newShelf: Shelf = {
      ...shelf,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      data: [newShelf],
      message: '创建货架成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '创建货架失败'
    };
  }
}

/**
 * 更新货架
 */
export async function updateShelf(id: string, shelf: Partial<Shelf>): Promise<ShelfResponse> {
  try {
    // 模拟 API 调用
    const updatedShelf: Shelf = {
      id,
      name: shelf.name || '货架A-001',
      location: shelf.location || '仓库A区',
      capacity: shelf.capacity || 100,
      currentStock: shelf.currentStock || 75,
      category: shelf.category || '电子产品',
      description: shelf.description,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      data: [updatedShelf],
      message: '更新货架成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '更新货架失败'
    };
  }
}

/**
 * 删除货架
 */
export async function deleteShelf(id: string): Promise<ShelfResponse> {
  try {
    // 模拟 API 调用
    return {
      success: true,
      message: '删除货架成功'
    };
  } catch (error) {
    return {
      success: false,
      message: '删除货架失败'
    };
  }
}
